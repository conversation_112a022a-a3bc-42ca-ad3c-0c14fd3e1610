import{r as o,co as i}from"./index.C1NIn1Y2.js";import{i as c}from"./inputUtils.CQWz5UKz.js";function E(r,n,s,t){o.useEffect(()=>{t||r!==n&&s(r)},[r,n,t,s])}function l(r,n,s,t,u,f=!1){return o.useCallback(e=>{const a=f?e.metaKey||e.ctrlKey:!0;!c(e)||!a||(e.preventDefault(),s&&n(),t.allowFormEnterToSubmit(r)&&t.submitForm(r,u))},[r,u,s,n,t,f])}function F({formId:r,maxChars:n,setDirty:s,setUiValue:t,setValueWithSource:u}){return o.useCallback(f=>{const{value:e}=f.target;n!==0&&e.length>n||(s(!0),t(e),i({formId:r})&&u({value:e,fromUi:!0}))},[r,n,s,t,u])}export{F as a,l as b,E as u};
